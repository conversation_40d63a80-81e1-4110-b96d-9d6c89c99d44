# Level Generation System - English

## A. Core Design: Hierarchical Organic Scaling

### Grid System
- 2.5D grid maps with visual separators
- Position: Map[x][y] arrays
- Grid units: rooms→regions→world scaling
- All operations use grid-based units

### Organic Scaling Principle
**"WYSIWYG"** - macro maps = precise micro-map thumbnails
- World→Region: area outlines = internal map miniatures
- Region→Building: building outlines = internal space miniatures
- **Entry System**: Any grid in target area = entrance trigger

## B. Physical Data Layers

### 1. Height & Hydrology
- `float Height, WaterLevel(0-1)` per grid
- Perlin noise → height maps → water flow → rivers/lakes

### 2. Climate/Biome (Parameter-Driven)
Physical params → biome weights (not labels):
- Water(0-1), Temp(0-1), Height(0-1) → biome weights
- Forest: W:0.1-0.5, T:0.2-0.6 | Desert: W:0-0.05, T:0.5-1.0

```cpp
struct FBiomeArchetype {
    float MinWater, MaxWater, <PERSON><PERSON><PERSON><PERSON>, Max<PERSON>emp, MinHeight, MaxHeight, Weight;
};
```

### 3. Pollution & Corruption
- `float Pollution(0-1), Corruption(0-1)`
- Sources: industry, war, magic, supernatural
- Effects: ecological degradation, monsters, resource depletion

### 4. Culture & Religion
- `ReligionID, FactionID` (separate systems)
- Supports: same-faith/different-factions, multi-faith/same-faction
- Effects: holy/forbidden lands, cultural conflicts

### 5. Elemental/Magic Fields
- `FElementalWeights{Fire, Water, Earth, Air}, MagicConcentration, FlowDirection`
- Effects: magical resources, elemental creatures, spell modifiers

### 6. Soil & Geology
- `SoilType, Fertility(0-1), MineralRichness(0-1)`
- Generation: height+water+temp+geological noise
- Effects: vegetation, farming, mining, construction

### 7. Ecological Evolution
- `SuccessionRate(0-1), Disturbance(0-1)`
- Dynamic biome changes from time, events, player actions

### 8. Seasonal & Climate Change
- Seasonal temp/water variations, extreme weather probability
- Dynamic resource cycles, seasonal hazards

### 9. Wind & Airflow (Physics-Based)
- Physics: pressure differentials → wind, terrain channels flow
- `WindSpeed, WindDirection, Turbulence, IsWindChannel`
- Effects: visibility, fire spread, temperature, sound, particles

### 10. Surface Cover
- `SurfaceCoverType, CoverThickness(0-1)`
- Types: snow, mud, ash, grass, bare ground
- Effects: movement speed, visibility, combat, resources

### 11. Biodiversity & Underground
- `Biodiversity(0-1), UndergroundLayers, HasUndergroundSpace`
- High diversity = stability, special resources, events
- Underground types from surface params (height+geology→caves)
- **Underground Maps**: World map enlarged to building-level precision
- Content: All tunnel layers with building-level detail

## C. Narrative Data Layers

### 1. Faction Control
- `FactionID, ControlStrength(0-1)` per grid
- Patterns: absolute sovereignty(1.0), neutral zones(~0), contested areas

### 2. History & Ruins
- `int Age` per region/grid
- Higher Age = ancient ruins, lost magic, prehistoric creatures

### 3. Elemental Affinity
- `FElementalWeights` determines magical resources, beings, spell effects

### 4. Threat Level (World Map Only)
- `float ThreatLevel(0-1)` - generated from other layers
- Sources: hostile factions, high Age, dangerous biomes

## D. Road Generation (Integrated)

### Resistance Calculation
- Resistance = f(height_diff×0.2 + water×0.3 + pollution×0.15 + threat×0.2 + ...)
- Wind channels reduce resistance
- A* pathfinding for optimal routes
- Dynamic updates from changes

## E. System Architecture

### 1. Map Representation
- 2.5D overhead view with separators
- 0.1cm height 3D grids with physical properties
- Wall system: boundary objects with HP
- Grid HP = base × terrain × soil × cover × elemental × modifiers

### 2. Building Hierarchy
- Buildings contain multiple rooms + furniture
- Hidden room interfaces (reserved)

### 3. Underground Tunnels
- Only in stable geology
- **Dedicated Underground Maps**: World map × magnification factor
- Distance = direct distance between entrance/exit points

### 4. Dynamic Generation
- All systems callable at runtime
- Event-driven updates via message bus
- Object pooling, unified resource management

## F. Layer Differentiation (RPG-Specific)

### 🌍 World Level - Strategic Exploration
**Core Function**: Long-term planning, resource management, macro narrative
- **Time Scale**: Days/weeks/months
- **Event Types**: Political events, seasonal changes, faction conflicts, disasters
- **Interaction**: Path selection, resource allocation, diplomatic decisions
- **Unique Mechanics**: Time progression, reputation system, strategic positioning

### 🏘️ Regional Level - Tactical Exploration
**Core Function**: Mid-term objectives, area control, social interaction
- **Time Scale**: Hours/days
- **Event Types**: Local quests, trade activities, regional conflicts, environmental changes
- **Interaction**: Location exploration, NPC interaction, resource gathering
- **Unique Mechanics**: Local reputation, dynamic quest chains, environmental interaction

### 🏠 Building Level - Tactical Execution
**Core Function**: Immediate decisions, precise operations, detail interaction
- **Time Scale**: Minutes/hours
- **Event Types**: Combat encounters, puzzle solving, item discovery, trap triggers
- **Interaction**: Turn-based combat, item manipulation, environmental puzzles
- **Unique Mechanics**: Tactical combat, environmental destruction, precision exploration

## G. Data Structure

```cpp
struct FMapCell {
    // Physical (12 params)
    float Height, WaterLevel, Temperature, Fertility, MineralRichness;
    int SoilType, SurfaceCoverType; float SurfaceCoverThickness;
    float WindSpeed, WindDirX, WindDirY, Turbulence; bool IsWindChannel;
    
    // Ecological (4 params)
    float Biodiversity, SuccessionRate, Disturbance;
    int UndergroundLayers; bool HasUndergroundSpace;
    
    // Elemental/Magic (7 params)
    float ElementFire, ElementWater, ElementEarth, ElementAir;
    float MagicConcentration, MagicFlowX, MagicFlowY;
    
    // Environmental (2 params)
    float Pollution, Corruption;
    
    // Narrative (5 params)
    int FactionID, ReligionID; float ControlStrength;
    int Age; float ThreatLevel;
    
    // Climate (3 params)
    float SeasonalTemperature, SeasonalWater, ExtremeClimateChance;
};
```

## H. Layer Inheritance Rules

**World Only (3)**: ThreatLevel, ExtremeClimateChance, ControlStrength
**Region Inherits (12)**: All physical params, biodiversity, underground, pollution, culture, elements, soil, evolution, seasons, wind, surface
**Room Inherits (10)**: Temperature, water, biodiversity, pollution, culture, elements, soil, evolution, wind, surface

## I. Event System Integration

### World Events
- **Political**: Dynasty changes, alliance shifts, wars
- **Natural**: Seasonal changes, disasters, climate anomalies
- **Economic**: Trade route changes, resource depletion, market fluctuations
- **Mystical**: Ancient ruins appear, magical phenomena, prophecies

### Regional Events
- **Social**: Festivals, community conflicts, ceremonies
- **Commercial**: Market days, caravan arrivals, price changes
- **Exploration**: New locations, hidden paths, secrets revealed
- **Conflict**: Bandit attacks, beast threats, territorial disputes

### Building Events
- **Combat**: Enemy encounters, boss battles, trap activation
- **Discovery**: Treasure finds, secret rooms, important items
- **Puzzle**: Mechanism puzzles, code breaking, path opening
- **Interaction**: Deep NPC dialogue, item examination, environment manipulation

---

**Core Philosophy**: Data-driven organic world generation with hierarchical consistency, physical realism, narrative depth, and layer-specific gameplay differentiation for turn-based event-driven RPG experience.
