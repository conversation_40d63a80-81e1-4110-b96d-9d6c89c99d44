# Level Generation System - Optimized

## A. Core Design: Hierarchical Organic Scaling

### Grid System & Position
- 2.5D grid maps with visual separators for depth
- Position: Map[x][y] arrays for indexing
- Grid units: rooms→regions→world scaling
- All operations use grid-based units

### Organic Scaling Principle
**"What you see is what you get"** - macro maps are precise micro-map thumbnails
- World→Region: irregular area outlines = precise miniatures of internal maps  
- Region→Building: building outlines = precise miniatures of internal spaces
- **Entry System**: Any grid in target area = entrance trigger

## B. Physical Data Layers

### 1. Height & Hydrology
- `float Height, WaterLevel(0-1)` per grid
- Perlin noise → height maps → water flow simulation → rivers/lakes

### 2. Climate/Biome (Parameter-Driven)
Physical params determine biome weights (not labels):
- Water(0-1), Temp(0-1), Height(0-1) → biome weights
- Forest: W:0.1-0.5, T:0.2-0.6 | Desert: W:0-0.05, T:0.5-1.0 | Volcano: W:0, T:0.8-1.0, H:high

```cpp
struct FBiomeArchetype {
    float MinWater, MaxWater, MinTemp, MaxTemp, MinHeight, MaxHeight, Weight;
};
```

### 3. Pollution & Corruption
- `float Pollution(0-1), Corruption(0-1)`
- Sources: industry, war, magic accidents, supernatural forces
- Effects: ecological degradation, monsters, resource depletion

### 4. Culture & Religion  
- `ReligionID, FactionID` (separate systems)
- Supports: same-faith/different-factions, multi-faith/same-faction
- Effects: holy/forbidden lands, cultural conflicts

### 5. Elemental/Magic Fields
- `FElementalWeights{Fire, Water, Earth, Air}, MagicConcentration, FlowDirection`
- Effects: magical resources, elemental creatures, spell modifiers

### 6. Soil & Geology
- `SoilType, Fertility(0-1), MineralRichness(0-1)`
- Generation: height+water+temp+geological noise
- Effects: vegetation, farming, mining, construction feasibility

### 7. Ecological Evolution
- `SuccessionRate(0-1), Disturbance(0-1)`
- Dynamic biome changes from time, events, player actions
- Creates "living" world ecosystem

### 8. Seasonal & Climate Change
- Seasonal temp/water variations, extreme weather probability
- Dynamic resource cycles, seasonal hazards

### 9. Wind & Airflow (Realistic Physics)
- Physics: pressure differentials drive wind, terrain channels flow
- `WindSpeed, WindDirection, Turbulence, IsWindChannel`
- Effects: visibility, fire spread, temperature, sound, particle dispersion
- Generation: pressure fields + terrain → wind channels in gaps/valleys

### 10. Surface Cover
- `SurfaceCoverType, CoverThickness(0-1)`
- Types: snow, mud, ash, grass, bare ground
- Effects: movement speed, visibility, combat, resource gathering

### 11. Biodiversity & Underground
- `Biodiversity(0-1), UndergroundLayers, HasUndergroundSpace`
- High diversity = stability, special resources, ecological events
- Underground types derived from surface params (height+geology→caves, volcano→lava tubes)

## C. Narrative Data Layers

### 1. Faction Control
- `FactionID, ControlStrength(0-1)` per grid
- Patterns: absolute sovereignty(1.0), neutral zones(~0), contested areas(split), forced coexistence

### 2. History & Ruins
- `int Age` per region/grid
- Higher Age = ancient ruins, lost magic, prehistoric creatures, historical mysteries

### 3. Elemental Affinity
- `FElementalWeights` determines magical resources, elemental beings, spell effects

### 4. Threat Level (World Map Only)
- `float ThreatLevel(0-1)` - generated from other layers
- Sources: hostile factions, high Age, dangerous biomes
- Regional manifestation: forbidden zones, faction strongholds, ancient guardians

## D. Road Generation (Integrated)

### World Roads
- Connect faction bases, resource nodes, landmarks
- Resistance = f(height_diff×0.2 + water×0.3 + pollution×0.15 + threat×0.2 + ...)
- Wind channels reduce resistance (natural passages)
- A* pathfinding for optimal routes
- Dynamic updates from ecological changes, disasters, player actions

### Regional Roads  
- Connect buildings, resource points, entrances
- Similar resistance calculation with local parameters
- Inherits world-level constraints

## E. System Architecture

### 1. Map Representation
- 2.5D overhead view with separator lines
- 0.1cm height 3D grids supporting physical properties
- Wall system: boundary objects with HP, separate from internal grids
- Grid HP = base × terrain × soil × cover × elemental × modifiers

### 2. Building Hierarchy
- Buildings contain multiple rooms + furniture
- Hidden room interfaces (reserved, not implemented)

### 3. Underground Tunnels
- Only in stable geology (parameter-determined)
- Connect rooms/regions/world, may be dead ends/branches
- Distance = sum of shortest paths between layers

### 4. Dynamic Generation
- All generation systems callable at runtime
- Event-driven updates via message bus
- Object pooling for performance
- Unified resource management with hot-reload support

## F. Data Structure

```cpp
struct FMapCell {
    // Physical (12 core params)
    float Height, WaterLevel, Temperature, Fertility, MineralRichness;
    int SoilType, SurfaceCoverType; float SurfaceCoverThickness;
    float WindSpeed, WindDirX, WindDirY, Turbulence; bool IsWindChannel;
    
    // Ecological (4 params)  
    float Biodiversity, SuccessionRate, Disturbance;
    int UndergroundLayers; bool HasUndergroundSpace;
    
    // Elemental/Magic (7 params)
    float ElementFire, ElementWater, ElementEarth, ElementAir;
    float MagicConcentration, MagicFlowX, MagicFlowY;
    
    // Environmental (2 params)
    float Pollution, Corruption;
    
    // Narrative (5 params)
    int FactionID, ReligionID; float ControlStrength;
    int Age; float ThreatLevel;
    
    // Climate (3 params)
    float SeasonalTemperature, SeasonalWater, ExtremeClimateChance;
};
```

## G. Layer Inheritance Rules

**World Only (3)**: ThreatLevel, ExtremeClimateChance, ControlStrength
**Region Inherits (12)**: All physical params, biodiversity, underground, pollution, culture, elements, soil, evolution, seasons, wind, surface
**Room Inherits (10)**: Temperature, water, biodiversity, pollution, culture, elements, soil, evolution, wind, surface

---

**Core Philosophy**: Data-driven organic world generation with hierarchical consistency, physical realism, and narrative depth. All systems integrate through parameter interdependence for emergent complexity.
