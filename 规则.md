// 关卡生成系统（Level Generation System）

## A. 核心设计原则：层级缩放与有机形态

### 0. 地图格子与位置表示
2.5D网格地图的视觉表现，使用分隔线,有层次感
- 地图格子的位置用二维数组（如Map[x][y]）表示，便于索引和操作。
- 地图上的基本单元（如建筑、区域）视为一个格子，所有操作、生成、交互均以格子为基本单位。
- 房间地图中的格子代表房间内部空间，区域地图的格子代表区域内部，世界地图的格子代表世界内部。

这是关卡生成系统的最高设计原则。宏观地图是微观地图的精确缩略图，所有地理和建筑特征都以不规则、有机的形态存在，保证了世界在不同尺度下的逻辑一致性和视觉真实感。

### 1. 有机且不规则的形态
- 地图上的基本单元（如建筑、区域）并非简单的正方形或长方形，而是由多个格子组合成的不规则形状。
- 构成一个建筑或区域的所有格子被视为一个单一、连续的实体。

### 2. 层级缩放与“所见即所得”
- **世界层 -> 区域层**: 世界地图上的不规则区域轮廓，是进入该区域后，其内部地图的精确微缩版。
- **区域层 -> 建筑层**: 区域地图上的不规则建筑轮廓，是进入该建筑后，其内部空间的精确微缩版。

### 3. 区域即入口
- 构成一个目标地点（区域或建筑）的所有不规则格子集合，都是入口。玩家移动到区域的任意格子上即可触发进入事件。

## B. 核心物理数据层 (Physical Layers)

这是将设计原则落地为具体实现的技术方案。地图的物理形态将由多个数值驱动的数据层叠加而成。

### 1. 高度与水文模拟 (Height & Hydrology)
- **数据结构**: 每个格子拥有 `float Height` (海拔) 和 `float WaterLevel` (水量, 0.0-1.0)。
- **生成逻辑**: 使用 Perlin Noise 等算法生成平滑的高度图，再通过模拟降雨和水流侵蚀的算法，让水从高处流向低处，最终汇集成拥有不同水深的河流与湖泊。

### 2. 气候/生态区设定 (Climate/Biome)
- **物理参数驱动的生态区表达**: 生态区（Biome）不再是单一标签，而是由一组物理参数（如水量、温度、高度等）决定。每种生态区对应一组典型的物理参数区间，地图生成时根据格子的物理参数，计算各生态区的权重，实现自然过渡和丰富的生态类型。生态区的水量参数与水系统（高度与水文模拟）直接联动。

- **主要物理参数**:
    - 水量（WaterLevel）：float 0.0~1.0（与水系统联动）
    - 温度（Temperature）：float 0.0~1.0
    - 高度（Height）：float 0.0~1.0（或直接用海拔）
    - 其他可扩展参数（如地表类型、特殊地貌等）

- **生态区典型参数区间示例**：

  | 生态区   | 水量        | 温度      | 高度   | 其他特征         |
      |----------|-------------|-----------|--------|------------------|
  | 森林     | 0.1~0.5     | 0.2~0.6   | 低~中  |                  |
  | 沙漠     | 0.0~0.05    | 0.5~1.0   | 低~中  |                  |
  | 草原     | 0.1~0.3     | 0.3~0.7   | 低~中  |                  |
  | 火山     | 0.0         | 0.8~1.0   | 高     | 岩浆/特殊地貌   |
  | 沼泽     | 0.5~1.0     | 0.2~0.6   | 低     | 地势低洼        |

- **生成逻辑**: 先用噪声算法生成高度、温度、水量等基础物理层，再根据这些物理参数，映射/归类出生态区权重。例如：高度高、温度高、水量低则火山权重高；水量高、温度适中则沼泽权重高；以此类推。

- **数据结构**: 每个格子拥有一个“生态权重”集合，而非单一的类型。权重值由物理参数映射而来。
    ```cpp
    // 生态区 archetype 配置结构体
    struct FBiomeArchetype
    {
        float MinWaterLevel, MaxWaterLevel;
        float MinTemperature, MaxTemperature;
        float MinHeight, MaxHeight;
        // 其他特征
        float Weight; // 可选：基础权重
    };
### 5. 污染与腐化 (Pollution & Corruption)
- **参数**: 每个格子拥有 `float Pollution`（污染度，0.0~1.0）、`float Corruption`（腐化度，0.0~1.0）。
- **生成逻辑**: 污染由工业、战争、魔法事故等动态产生，腐化多与超自然/邪恶力量相关。
- **影响**: 污染区生态退化、怪物增多、资源枯竭，腐化区出现特殊怪物、诅咒、异象。

### 6. 文化/信仰影响 (Culture & Religion)
- **参数**: 每个格子拥有 `ReligionID`（信仰ID/分布权重），与 `FactionID`（派系ID）分离。
- **生成逻辑**: 信仰分布可与派系重叠或独立，支持“同信仰不同派系”、“同派系多信仰”或“派系即信仰”等多样组合。
- **影响**: 圣地/禁地、信仰冲突、文化特征等影响事件、建筑、NPC行为。

### 7. 元素/魔法能量场 (Elemental/Magic Fields)
- **参数**: 魔法能量浓度、流向等作为元素权重（FElementalWeights）的一部分，特殊区域可有高能量节点或流动。
- **生成逻辑**: 魔法能量与元素分布集成，影响魔法生物、法术效果、特殊资源。

### 8. 土壤类型/地质层 (Soil & Geology)
- **参数**: 每格子拥有 `SoilType`（壤土、砂土、黏土、火山灰土、岩石等）、`float Fertility`（肥力）、`float MineralRichness`（矿藏丰度）。
- **生成逻辑**: 结合高度、水量、温度等物理参数，叠加地质噪声生成不同土壤类型和矿藏分布。
- **影响**: 影响植被、农田、矿产、建筑可行性等。

### 9. 生态演化/动态变化 (Biome Succession & Dynamics)
- **参数**: 每格子拥有 `float SuccessionRate`（演化速率）、`float Disturbance`（外部干扰强度）。
- **生成逻辑**: 生态区随时间、事件动态变化，受玩家行为、自然灾害等影响。
- **影响**: 生态区会演化、退化或恢复，形成“活”的世界。

### 10. 季节与气候变化 (Seasonality & Climate Change)
- **参数**: 季节性温度、水量变化幅度，极端气候事件概率。
- **生成逻辑**: 随时间推进动态改变生态区分布、资源产出、威胁等级等。
- **影响**: 季节性沼泽/湖泊、极端天气、资源周期性变化。

### 11. 风向与气流 (Wind & Airflow)
- **参数**: 主导风向、风速、气流扰动。
- **生成逻辑**: 影响气候分布、降水、火灾蔓延、飞行单位/魔法等。
- **影响**: 风口地带风能资源丰富，火山灰随风扩散影响下风区生态。

### 12. 地表覆盖类型 (Surface Cover)
- **参数**: 每个格子拥有 `SurfaceCoverType`（如积雪、落叶、泥泞、火山灰、草皮、裸地等）、`float CoverThickness`（覆盖厚度，0.0~1.0）。
- **生成逻辑**: 根据气候、季节、生态区、事件等动态生成和变化。
- **影响**: 影响移动速度、能见度、战斗、资源采集等。

### 13. 风向与气流 (Wind & Airflow) —— 真实风模拟
- **物理基础**: 风由气压差（温度/高度差）驱动，地形（高山、缺口）决定风的主通道（木桶理论）。
- **参数**: 每格子拥有 `float WindSpeed`、`FVector2D WindDirection`、`float WindTurbulence`、`bool IsWindChannel`。
- **影响**: 风影响能见度（Visibility）、火灾蔓延（FireSpreadRate）、温度修正（TemperatureModifier）、声音传播（SoundPropagation）、粒子扩散（ParticleDispersion）等。
- **生成逻辑**: 基于气压场和地形动态生成风场，风速在缺口、峡谷增强。

---

## C. 核心叙事数据层 (Narrative Layers)

在物理世界的基础上，我们叠加社会、历史和神秘学的数据层，为世界赋予深度和灵魂。

### 1. 文明与派系控制 (Civilization & Faction Control)
- **数据结构**: 每个格子拥有 `FactionID` 和 `ControlStrength` (控制力, 0.0-1.0)。
- **实现模式**:
    - **绝对主权区**: 单一派系控制力接近1.0。
    - **中立/三不管地带**: 所有派系控制力都极低。
    - **共治/盟约区**: 多个友好派系共享较高的控制力。
    - **前线/冲突区**: 多个敌对派系在此区域平分秋色。
    - **荒诞的共存区**: 敌对派系因某种外部限制（如魔法契约）被迫和平共处。

### 2. 历史与遗迹深度 (History & Ruin Depth)
- **数据结构**: 每个区域或格子拥有一个 `int Age` (年代) 属性。
- **影响**: `Age` 值越高，该区域出现**古代遗迹、失落魔法、史前生物和历史谜题**的权重就越高。

### 3. 魔法元素亲和度 (Elemental Affinity)
- **数据结构**: 每个格子拥有一套“元素权重”，例如 `FElementalWeights { float Fire; float Water; ... }`。
- **影响**: 决定该区域的**魔法资源、元素生物**，以及对不同系别法术的**增益或减益效果**。

### 4. 威胁等级 (Threat Level)
- **核心逻辑**: **威胁等级只存在于世界地图**。它由其他数据层联动生成，作为在区域地图中具体表现“威胁”的依据。
- **数据结构**: 世界地图的每个格子拥有 `float ThreatLevel` (0.0-1.0)。
- **威胁来源**:
    - **派系**: 敌对派系的核心区和冲突区会提升威胁等级。
    - **历史**: `Age` 越高的区域，威胁等级越高（古代守护者）。
    - **地理**: 险恶的生态区（如火山、沼泽）天生具有高威胁。
- **在区域地图中的具体表现**:
    - **独立的禁地区域**: 世界地图上高威胁、无派系控制的区域。
    - **派系内部的威胁点**: 派系领地内因高`Age`或险恶地形产生的危险点（如古墓、兽穴、强盗营地）。
    - **派系基地**: 对于非盟友玩家来说，是守卫森严的高威胁区域。

### 3. 生物多样性指数 (Biodiversity Index)
- **参数**: 每个格子拥有 `float Biodiversity` (0.0~1.0)，表示物种丰富度。
- **生成逻辑**: 由生态区类型、土壤肥力、水量、气候等物理参数共同决定。多样性高的区域更稳定，极端环境（如沙漠、火山）多样性低。
- **影响**: 高多样性区有特殊生物、药材、生态事件，生态失衡会引发灾害。

### 4. 地下空间/多层地形 (Underground/Layered Terrain)
- **参数**: 每个格子拥有 `int UndergroundLayers`（地下层数），以及 `bool HasUndergroundSpace`（是否有地下空间）。
- **生成逻辑**: 地表物理参数（如高度、地表类型、地质层）决定地下空间的分布和类型。例如山地多洞穴，火山下有熔岩洞，河流下有地下河。
- **类型映射**: 地下空间类型不单独存储标签，而是由物理参数动态映射推导。例如：
    - 高度高+岩石层→洞穴
    - 火山地表+高温→熔岩洞
    - 河流地表+高水量→地下河
- **影响**: 地下空间可探索、建造、战斗，连接不同地表区域，拥有独特生物、资源和剧情。

世界中的道路计算（集成现有设定）
世界地图（宏观层）上的道路不是随机生成，而是数据驱动的路径规划，集成物理、生态、叙事、气候等多个设定，确保道路“有机、实用且真实”。道路可以视为连接派系区域、资源点、奇观等的“网络”，优先选择低阻力、低威胁的路径，模拟真实世界的贸易/迁徙路线。
计算流程（集成设定）
起点/终点确定：基于叙事层（如派系控制区、圣地、资源富集区）或特殊地貌（如高矿藏丰度区）自动选定起点/终点。例如，派系基地间需连接道路，矿藏丰度高的格子会吸引贸易路线。
路径阻力计算：每个格子计算“阻力值”（float 0.0~1.0），集成多个设定参数：
物理层：高度差大（陡坡）、水量高（沼泽/河流）、土壤类型（如泥泞黏土）增加阻力；平坦、低水量、坚实土壤减少阻力。
生态区：高生物多样性或极端生态（如火山、沙漠）增加阻力（野生动物干扰、沙尘暴）；草原/森林可能降低阻力（易穿越）。
地表覆盖：泥泞/积雪增加阻力（移动慢）；草皮/裸地减少阻力。
风与气流：逆风或高湍流增加阻力（影响行进）；顺风减少阻力；风道（如山口）作为“低阻力通道”（木桶理论集成：风从缺口进入，道路也优先利用这些自然通道）。
污染/腐化：高污染/腐化增加阻力（怪物、诅咒、资源枯竭）。
季节/气候：冬季积雪或雨季水量高时动态增加阻力；极端气候事件（如风暴）临时阻断道路。
叙事层：高威胁等级、敌对派系区增加阻力；盟友派系区减少阻力。
地下空间：如果有地下通道，可作为“捷径”降低总体阻力（例如地下河作为水路）。
阻力公式示例（伪代码）：
Apply to 规划.md
float Resistance = (HeightDiff * 0.2) + (WaterLevel * 0.3) + (Pollution * 0.15) + (ThreatLevel * 0.2) + ...;  // 集成所有相关参数
if (IsWindChannel && WindDirection aligns with path) Resistance -= 0.1;  // 风道优惠
路径生成算法：使用A或Dijkstra路径算法，优先低阻力路径。集成动态变化：生态演化、季节变化、玩家行为（如破坏/修建）会实时更新道路（例如洪水淹没路径后重算）。
道路类型与表现：根据路径参数生成类型（如土路、石路、桥梁）；集成元素/魔法：高魔法浓度区可能有“魔法浮桥”。
集成动态性：道路不是静态的，受生态演化（退化/恢复）、灾害链（地震破坏）、派系AI（修建/摧毁）影响，形成“活”的网络。
示例集成效果
一条从山地到平原的道路会优先通过风道缺口（木桶理论），避开高污染沼泽，但如果季节水量高，可能临时绕道地下空间。
2. 区域中的道路计算（集成现有设定）
   区域地图（中观层）是世界地图的精确微缩版，所以道路计算类似但更细化，焦点在连接建筑/资源点/入口等，集成局部参数。区域道路可以视为世界道路的“分支”或“内部网络”。
   计算流程（集成设定）
   起点/终点确定：基于区域内的叙事点（如派系据点、圣地、遗迹）或物理点（如矿藏、河流）。
   路径阻力计算：类似世界层，但更注重微观参数：
   物理/生态：局部高度差、水量、地表覆盖（泥泞增加阻力）；生物多样性高可能生成“野生小径”。
   风/气候：局部风速影响路径（如避开高湍流区）；季节变化动态调整（如冬季雪路）。
   污染/腐化：高腐化区道路易“扭曲”（生成陷阱/怪物路径）。
   地下空间：区域内地下层可作为隧道连接建筑。
   叙事：威胁点（如兽穴）避开；信仰区可能有“神圣路径”（低阻力但有规则限制）。
   路径生成算法：A算法，生成更密集的网络（如村落间小路）。集成动态：玩家行为（如修建桥梁）或事件（如火灾）实时重算。
   道路类型与表现：更精细，如木板路（沼泽）、石阶（山地），集成元素：高火元素区可能有“熔岩桥”。
   与世界层的区别
   世界道路更宏观（连接区域），区域道路更微观（连接建筑/点位），但继承世界参数（如世界高威胁区会影响区域道路生成）。


只影响世界地图（宏观层，3项）
威胁等级（ThreatLevel）：文档明确“只存在于世界地图”，用于全局威胁评估，不带入微观。
极端气候事件概率（ExtremeClimateChance）：宏观天气事件（如全球风暴），不细化到房间。
派系控制强度（ControlStrength）：宏观领土控制，区域/房间用派系ID继承但不计算强度。

影响区域地图（中观层，继承世界参数，12项）
物理参数（Height, WaterLevel, Temperature等）：细化地形/水文。
生态区权重/生成：映射为区域生态细节。
生物多样性指数（Biodiversity等）：影响区域野生生物/事件。
地下空间（UndergroundLayers等）：扩展为区域地下地图。
污染与腐化（Pollution, Corruption）：影响区域退化/怪物。
文化/信仰影响（ReligionID等）：生成区域圣地/禁地。
元素/魔法能量场：影响区域法术/资源。
土壤类型/地质层（SoilType等）：影响区域资源/建筑。
生态演化/动态变化（SuccessionRate等）：区域生态实时变化。
季节与气候变化（SeasonalTemperature等）：区域天气周期。
风向与气流（WindSpeed等）：区域风场影响移动/战斗。
地表覆盖类型（SurfaceCoverType等）：区域地面细节。

影响房间地图（微观层，继承区域参数，10项）
物理参数（Temperature, WaterLevel等）：室内环境模拟。
生态区细节：室内植物/小生物。
生物多样性：室内生态事件。
污染与腐化：室内诅咒/退化。
文化/信仰：室内装饰/规则（如神殿禁忌）。
元素/魔法：室内法术增益/元素生物。
土壤类型/地质：室内地板/墙壁材质。
生态演化：室内动态变化（如霉菌生长）。
风向与气流：室内通风/灰尘。
地表覆盖：室内地面覆盖（如尘土/血迹）。
---

## D. 总结

本关卡生成系统的核心是**“所见即所得的有机缩放”**与**“多层数据驱动的自然模拟”**。
1.  所有地图层级共享一套按比例缩放的**不规则**轮廓。
2.  地图的**物理特征**（高度、水文、生态区）和**叙事特征**（派系、历史、威胁、元素）均由**连续的数值**通过算法生成，实现了**平滑过渡**的真实感。
3.  通过在不同层级注入**差异化的 Gameplay 机制**，确保每一层都有其独特的游戏体验和探索价值。

struct FMapCell {
// 物理层
float Height;                // 海拔
float WaterLevel;            // 水量
float Temperature;           // 温度
int SoilType;                // 土壤类型（枚举）
float Fertility;             // 肥力
float MineralRichness;       // 矿藏丰度

    // 生态区与动态
    float Biodiversity;          // 生物多样性指数
    float SuccessionRate;        // 生态演化速率
    float Disturbance;           // 外部干扰强度

    // 地表覆盖
    int SurfaceCoverType;        // 覆盖类型（枚举）
    float SurfaceCoverThickness; // 覆盖厚度

    // 风与气流
    float WindSpeed;             // 风速
    float WindDirX;              // 风向X分量
    float WindDirY;              // 风向Y分量
    float WindTurbulence;        // 湍流
    bool IsWindChannel;          // 是否为风口/风道
    float Visibility;            // 能见度
    float FireSpreadRate;        // 火灾蔓延速率
    float TemperatureModifier;   // 风对温度修正
    float SoundPropagation;      // 声音传播距离
    float ParticleDispersion;    // 粉尘/花粉等扩散速率

    // 元素/魔法
    float ElementFire;           // 火元素权重
    float ElementWater;          // 水元素权重
    float ElementEarth;          // 土元素权重
    float ElementAir;            // 风元素权重
    float MagicConcentration;    // 魔法能量浓度
    float MagicFlowX;            // 魔法能量流向X分量
    float MagicFlowY;            // 魔法能量流向Y分量

    // 污染与腐化
    float Pollution;             // 污染度
    float Corruption;            // 腐化度

    // 地下空间
    int UndergroundLayers;       // 地下层数
    bool HasUndergroundSpace;    // 是否有地下空间

    // 叙事与社会
    int FactionID;               // 派系ID
    int ReligionID;              // 信仰ID
    float ControlStrength;       // 派系控制力
    int Age;                     // 年代
    float ThreatLevel;           // 威胁等级

    // 季节与气候
    float SeasonalTemperature;   // 季节性温度修正
    float SeasonalWater;         // 季节性水量修正
    float ExtremeClimateChance;  // 极端气候事件概率
};

---

## E. 系统架构与通用机制

1. **地图表现与格子定义**
    - 每层地图（世界、区域、房间）均为2.5D俯视角地图，视觉表现采用分隔线，突出层次感。
    - 地图格子为高0.1cm的3D格子，支持高度、体积等物理属性。
    - 房间地图的墙体由地图边界的黑色0.1x0.1线条表示，墙体血条由边界对象统一管理，破坏墙体时减少对应边界血条，血条为0时该段墙体被打通。
    - 房间内部格子仅代表空间内容（地面、家具等），不直接存储墙体信息，实现空间与墙体的解耦。
    - 每个格子都有血条（耐久度），可被破坏。
    - **血条（耐久度）机制说明**：
        - 血条值由地形类型、土壤/岩石类型、生态区、覆盖层厚度、魔法/元素影响等参数共同决定。
            - 例如：岩石/山地格子血条高，泥土/沙地格子血条低，魔法强化区血条提升，腐化/污染区血条降低。
            - 地表覆盖厚度大（如厚积雪、厚火山灰）可临时提升血条。
        - 具体公式可为：
          血条 = 基础血条 × 地形系数 × 土壤系数 × 覆盖层系数 × 元素/魔法系数 × 其他修正
        - 世界地图格子血条最高，因其代表最大空间范围和最坚固结构，破坏最难。
        - 区域地图格子血条中等，房间地图格子血条最低。
        - 血条可随生态演化、污染、魔法事件等动态变化，玩家/AI行为（如修建、加固、腐蚀）也可影响血条。

2. **建筑与房间层级**
    - 建筑层内部包含多个房间和家具，建筑是“房屋”概念，不只是单房间。
    - 房间预留隐藏房间接口，暂不实现。

3. **地下通道机制**
    - 建筑可有地下通道，地下通道只在坚固(根据相关设定判定是否坚固)地下才有，非必然存在。
    - 地下通道可通往同层其他房间、区域、世界地图，也可能是死路、分叉路、地牢（地牢为新地图，留接口，暂不实现）。
    - 地下通道的实际距离为：房间与屋边缘的最短距离 + 屋与区域的最短距离 + 区域与世界出口的最短距离（直线最短距离之和）。

4. **系统运行与生成**
    - 世界生成系统、区域生成系统、房屋生成系统均可在游戏运行时被调用，用于动态生成新的地图。

2. **结构体与枚举管理**
    - 所有地图相关结构体、枚举（如FMapCell、ESoilType、ESurfaceCoverType等）集中到统一工具类（如FMapUtil）中管理，便于跨系统调用和维护。

3. **系统解耦与事件机制**
    - 各子系统（如地图、生态、叙事、AI等）通过事件/消息总线（Event/Message Bus）进行交互，解耦依赖，便于扩展和维护。
    - 事件总线支持异步消息、订阅/发布机制。

4. **对象池与缓存管理**
    - 所有动态对象（如地图格子、实体、特效等）由通用对象池（Object Pool）统一管理，提升性能，减少GC压力。需实现高效的对象池系统。
    - 缓存采用通用缓存系统（Cache System）统一管理，支持多级缓存、自动失效、资源复用。

5. **资源管理**
    - 可复用资源（如贴图、模型、音效等）由统一资源管理类（ResourceManager）集中管理，支持资源加载、引用计数、热更新等。

> 以上机制确保系统高效、解耦、易扩展，便于与其他模块协作和未来功能拓展。